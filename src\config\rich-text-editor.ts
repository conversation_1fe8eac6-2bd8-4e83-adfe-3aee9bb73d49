import { LOCAL_UPLOAD_IMAGE_API_URL, LOCAL_STATIC_RESOURCES_BASE_URL } from './api';


export const RichTextEditorConfig = Object.freeze({
  // 配置说明文档 https://fex.baidu.com/ueditor/#start-config
  iframeCssUrl: `${LOCAL_STATIC_RESOURCES_BASE_URL}css/pima-editor/latest/default.min.css`,

  // 以下两个为特殊自定义的配置，不存在于ueditor配置
  uploadImageApiUrl: LOCAL_UPLOAD_IMAGE_API_URL, // 上传图片接口
  staticResourcesBaseUrl: LOCAL_STATIC_RESOURCES_BASE_URL, // 静态资源地址

  wordCount: false, // 是否开启字数统计
  maximumWords: 500, // 允许的最大字符数
  zIndex: 1, // 加大层叠数，避免在模态窗中被遮挡
});
