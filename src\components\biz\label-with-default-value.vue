<script setup lang="ts">
import { EmptyText } from '@/consts/empty-text';
import { isNothing } from '@/utils/is';


interface Props {
  label: string;
  value?: string | number;
  span?: string | number;
}

withDefaults(defineProps<Props>(), {
  value: '',
  span: 12,
});
</script>


<template>
  <Col :span="span">
    <Row>
      <Col class="label">
        {{ label }}
      </Col>
    </Row>
    <Row>
      <Col class="value">
        {{ isNothing(value)? EmptyText: value }}
      </Col>
    </Row>
  </Col>
</template>


<style lang="less" scoped>
.label {
  margin-bottom: 8px;
  color: #777;
  font-weight: 400;
  font-size: 15px;
}

.value {
  min-height: 20px;
  margin-bottom: 20px;
  color: #111;
  font-weight: 400;
  font-size: 16px;
  white-space: pre-wrap;
}
</style>
