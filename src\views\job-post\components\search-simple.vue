<script setup lang="ts">
import { inject } from 'vue';

import type { SearchSimpleModelType } from '^/types/job-post';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectDateRange from '@/components/common/select-date/select-date-range.vue';
import SelectJobPostStatus from '@/components/biz/select/job-post-status.vue';
import SelectJobPostPublishStatus from '@/components/biz/select/job-post-publish-status.vue';

import { MENU_NAME_INJECTION_KEY, SiderMenuCodes } from '@/config/sider-menu';
import { namespaceT } from '@/helps/namespace-t';


interface Props {
  exporting: boolean;
}

withDefaults(defineProps<Props>(), {
  exporting: false,
});


const emit = defineEmits<{
  'on-add': []
  'on-export': [],
  'on-search': []
}>();

const t = namespaceT('jobPost');
const model = defineModel<SearchSimpleModelType>();
const menuName = inject<GetMenuName>(MENU_NAME_INJECTION_KEY);


const emitSearch = () => {
  emit('on-search');
};
</script>


<template>
  <WrapperSearchSimple :title="menuName((SC:typeof SiderMenuCodes) => SC.JobPost)">
    <PairLabelItem>
      <SelectJobPostStatus
        v-model="model.status"
        class="w-160 simple-search"
        clearable
        :placeholder="t('placeholder.status')"
        @on-change="emitSearch"
      />
    </PairLabelItem>

    <PairLabelItem>
      <SelectJobPostPublishStatus
        v-model="model.publishStatus"
        class="w-160 simple-search"
        clearable
        :placeholder="t('placeholder.publishStatus')"
        @on-change="emitSearch"
      />
    </PairLabelItem>

    <PairLabelItem>
      <SelectDateRange
        v-model:start-date="model.creStartTime"
        v-model:end-date="model.creEndTime"
        class="simple-search"
        :start-placeholder="t('placeholder.creStartTime')"
        :end-placeholder="t('placeholder.creEndTime')"
        @on-change="emitSearch"
      />
    </PairLabelItem>


    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="t('placeholder.keyword')"
        class="w-300"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>

    <template #right>
      <Button
        v-if="$can((P)=>P.JobPost.Export)"
        class="pima-btn mr-15"
        type="default"
        :loading="exporting"
        @click="emit('on-export')"
      >
        <div class="btn-content">
          <img
            src="@/assets/img/icon/export.png"
            class="icon"
          >
          {{ t('action.export') }}
        </div>
      </Button>

      <Button
        v-if="$can((P)=>P.JobPost.Add)"
        class="pima-btn"
        type="default"
        @click="emit('on-add')"
      >
        <div class="btn-content">
          <Icon
            type="ios-add"
            class="icon"
          />
          {{ t('action.add') }}
        </div>
      </Button>
    </template>
  </WrapperSearchSimple>
</template>


<style lang="less" scoped>
.simple-search{
  &:deep(.pima-date-picker.date-picker){
    .ivu-input{
      color:#204394;
      background-color: #eef4ff;
      border: 1px solid #c7d7fe;

      &::placeholder{
        color: #204394;
      }
    }
  }
}

.btn-content{
  display: flex;
  align-items: center;

  .icon{
    color:inherit;
    font-size:24px;
  }

  img{
    width:20px;
    height:20px;
    margin-right: 4px;
  }
}

</style>
