import { createInputRules, createSelectRules } from '@/helps/rules';

export const createFormRules = () => {
  return {
    /** 岗位名称 */
    name: [createInputRules()],
    /** 岗位类型 */
    name1: [createSelectRules({ type: 'number' })],
    /** 研究方向 */
    name2: [createInputRules()],
    /** 性别要求 */
    name3: [createSelectRules()],
    /** 所需专业 */
    name4: [createInputRules()],
    /** 需求人数 */
    name5: [createInputRules()],
    /** 工作内容 */
    name6: [createInputRules()],
    /** 实践地址 */
    name7: [createInputRules()],
    /** 详细地址 */
    name8: [createInputRules()],
    /** 报名截止时间 */
    name9: [createInputRules()],
    /** 其他要求 */
    name10: [createInputRules()],
    /** 发布类型 */
    name11: [createSelectRules()],
  };
};
