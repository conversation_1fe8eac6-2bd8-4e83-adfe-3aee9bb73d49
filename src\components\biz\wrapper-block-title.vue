<script setup lang="ts">
defineProps<{
  title?: string;
}>();
</script>

<template>
  <div class="wrapper-block">
    <div class="gap-list-10 v-center my-20">
      <div class="title-bar gap-item" />
      <div class="title-text gap-item">
        {{ title }}
      </div>
    </div>

    <div
      v-if="$slots.default"
      class="block-content px-20"
    >
      <slot />
    </div>
  </div>
</template>

<style lang="less" scoped>
.title-bar {
  width: 4px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 5px;
}

.title-text {
  color: #111;
  font-weight: 500;
  font-size: 18px;
}
</style>
