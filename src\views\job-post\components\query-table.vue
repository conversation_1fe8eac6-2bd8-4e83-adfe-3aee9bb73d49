<script lang="ts" setup>
import { getCurrentInstance, onBeforeMount } from 'vue';

import type { ClassMgtListItemType } from '^/types/class-management';
import type { Auth } from '@/config/auth';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import DefaultText from '@/components/common/default-text.vue';
import UpAndDownText from '@/components/biz/up-and-down-text.vue';

import DetailIcon from '@/assets/img/table-action/detail.png';
import EditIcon from '@/assets/img/table-action/edit.png';
import ReapplyIcon from '@/assets/img/table-action/reapply.png';
import DeleteIcon from '@/assets/img/table-action/delete.png';

import { isY } from '@/helps/y-o-n';
import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';

import { createColumns } from '../helps/columns';
import { JobPostStatus } from '@/consts/job-post';
import { useJobPostStatusStore } from '@/store/data-tags/job-post-status';
import { useJobPostPublishStatusStore } from '@/store/data-tags/job-post-publish-status';
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { mockJobPostData } from '../helps/mock-data';


defineProps<{
  data: ClassMgtListItemType[];
  loading: boolean;
}>();

const emit = defineEmits<{
  'on-edit': [id:number]
  'on-reapply': [id:number]
  'on-view':[id:number]
  'on-delete': [id:number]
}>();

type EmitType = 'edit' | 'reapply' | 'view' | 'delete';


const vm = getCurrentInstance();
const t = namespaceT('jobPost');
const td = namespaceT('dateFormat');
const columns = createColumns();

const postTypeStore = usePostTypeStore();
const statusStore = useJobPostStatusStore();
const publishStatusStore = useJobPostPublishStatusStore();


const onEmit = (id:number, type: EmitType) => {
  switch (type) {
    case 'edit':
      emit('on-edit', id);
      break;
    case 'reapply':
      emit('on-reapply', id);
      break;
    case 'view':
      emit('on-view', id);
      break;
    case 'delete':
      emit('on-delete', id);
      break;
    default:
      break;
  }
};

const can = (type: string) => {
  return vm?.proxy?.$can?.((P: typeof Auth) => P.JobPost?.[type]) ?? false;
};


const actions = [
  {
    label: t('action.view'),
    icon: DetailIcon,
    triggerEvent: (row: ClassMgtListItemType) => onEmit(row.id, 'view'),

  },
  {
    label: t('action.edit'),
    icon: EditIcon,
    triggerEvent: (row: ClassMgtListItemType) => onEmit(row.id, 'edit'),
    can: (row: ClassMgtListItemType) => true || (isY(row.id || 'Y') && can('Edit')),
  },
  {
    label: t('action.reapply'),
    icon: ReapplyIcon,
    triggerEvent: (row: ClassMgtListItemType) => onEmit(row.id, 'reapply'),
    can: (row: ClassMgtListItemType) => true || (isY(row.id || 'Y') && can('Reapply')),
  },
  {
    label: t('action.delete'),
    icon: DeleteIcon,
    triggerEvent: (row: ClassMgtListItemType) => onEmit(row.id, 'delete'),
    can: (row: ClassMgtListItemType) => true || (isY(row.id || 'Y') && can('Delete')),
  },
];


const formatTime = (date: Date | string, type:string = 'dateTime') => {
  return dateFormatSTZ(new Date(date), td(type));
};

const showPublishData = (row:ClassMgtListItemType) => {
  const statusList = [JobPostStatus.Passed, JobPostStatus.Aborted];
  return statusList.includes(row.status);
};


onBeforeMount(() => {
  postTypeStore.loadDataIfNeeded();
  statusStore.loadDataIfNeeded();
  publishStatusStore.loadDataIfNeeded();
});

</script>


<template>
  <CTable
    :columns="columns"
    :data="mockJobPostData"
    :loading="loading"
  >
    <!-- 岗位名称 -->
    <template #postName="{ row }">
      <UpAndDownText
        :up="row.postName"
        :down="postTypeStore.getTextByCode(row.postType) || '??'"
      />
    </template>


    <!-- 实践地址 -->
    <template #practiceAddress="{ row }">
      {{ row.practiceAddress }}
    </template>

    <!-- 报名截止时间 -->
    <template #deadline="{ row }">
      {{ formatTime(row.deadline,'date') }}
    </template>

    <!-- 创建时间 -->
    <template #createTime="{ row }">
      <DefaultText :text="formatTime(row.creTime)" />
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <DefaultText :text="statusStore.getTextByCode(row.status)" />
    </template>

    <!-- 发布状态/发布时间 -->
    <template #publishStatusAndTime="{ row }">
      <UpAndDownText
        v-if="showPublishData(row.status) || true"
        :up="publishStatusStore.getTextByCode(row.publishStatus)"
        :down="formatTime(row.publishTime|| new Date())"
      />

      <DefaultText
        v-else
        :text="null"
      />
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :limit="3"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>
</template>
