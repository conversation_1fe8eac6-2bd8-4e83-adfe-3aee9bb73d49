<script setup lang="ts">
import { namespaceT } from '@/helps/namespace-t';

interface Props {
  total: number;
}

withDefaults(defineProps<Props>(), {
  total: 0,
});

const isChecked = defineModel<boolean>();

const t = namespaceT('common.table.text');

</script>


<template>
  <div class="checkbox-whole-table pima-checkbox-group">
    <Checkbox
      v-model="isChecked"
      class="check-all pima-checkbox"
    >
      {{ t('checkAll', { total }) }}
    </Checkbox>

    <Poptip
      :content="t('popTips')"
      placement="right"
    >
      <Icon
        type="ios-alert-outline"
        class="icon-tips"
      />
    </Poptip>
  </div>
</template>


<style scoped lang="less">
.checkbox-whole-table {
  display: flex;
  align-items: center;
  padding: 0 4px;

  :deep(.ivu-checkbox) {
    .ivu-checkbox-inner::after {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(45deg) scale(1);
    }
  }

  .check-all {
    margin-right: 0;
    color: #003c7d;
    font-size: 14px;
  }

  .icon-tips{
    width: 16px;
    color: var(--primary-color);
    font-size: 16px;
  }
}
</style>
