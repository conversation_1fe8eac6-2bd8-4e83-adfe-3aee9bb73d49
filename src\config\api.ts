import { PUBLIC_PATH } from './public-path';

export const LOCAL_BDC_CORE_API_BASE_URL = `${PUBLIC_PATH}x-bdc-core-api`; // 公共接口API
export const LOCAL_BDC_ARCH_API_BASE_URL = `${PUBLIC_PATH}x-bdc-arch-api`; // 统一数据接口API
export const LOCAL_BDC_SERVICE_API_BASE_URL = `${PUBLIC_PATH}x-bdc-service-api`; // 统一应用接口API
export const LOCAL_BDC_EXPORT_API_BASE_URL = `${PUBLIC_PATH}x-bdc-export-api`; // 导出接口API
export const LOCAL_BDC_IMPORT_API_BASE_URL = `${PUBLIC_PATH}x-bdc-import-api`; // 导入接口API
export const LOCAL_BDC_DFS_API_BASE_URL = `${PUBLIC_PATH}x-bdc-dfs-api`; // 附件接口API
export const LOCAL_USER_INFO_API_BASE_URL = `${PUBLIC_PATH}x-user-info-api`; // 用户信息接口API
export const LOCAL_UPLOAD_IMAGE_API_URL = `${LOCAL_BDC_DFS_API_BASE_URL}/attachments/editor/upload`;
export const LOCAL_STATIC_RESOURCES_BASE_URL = `${PUBLIC_PATH}x-static-resources/`; // 靜態資源(使用UEditor时调用)

export const LOCAL_DOUBLE_SELECTION_API_BASE_URL = `${PUBLIC_PATH}x-double-selection-api`; // 岗位双选应用 接口API

export const API_SALT = 'sigs_double_selection'; // 接口盐值

// 接口日期格式
export const ApiDateFormat = Object.freeze({
  DATE_TIME: "yyyy-MM-dd'T'HH:mm:ssxxx",
  DATE: 'yyyy-MM-dd',
});

// 接口所在服务器时区
export const API_SERVER_TIME_ZONE = 'Asia/Shanghai';
