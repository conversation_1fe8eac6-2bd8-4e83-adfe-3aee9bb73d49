<script setup lang="ts">
import { ref, onBeforeMount } from 'vue';
import {Cascader} from 'view-ui-plus'

import { RegionsApi } from '@/api/client/regions';
import { openToastError } from '@/helps/toast';


const data = ref([]);

const fetch = async (id:number = undefined) => {
  const api = new RegionsApi();
  if (id) {
    api.params = {
      parentId: id,
    };
  }
  const res = await api.send();

  if (Array.isArray(data.value) && data.value.length === 0) {
    data.value = res;
  }

  return res;
};


const loadData = async (item) => {
  try {
    Object.assign(item, { loading: true });
    const list = await fetch(item.id);

    Object.assign(item, { children: list });
  } catch (error) {
    openToastError(error.message);
  } finally {
    Object.assign(item, { loading: false });
  }
};


onBeforeMount(() => {
  fetch();
});

</script>


<template>
  <Cascader
    v-width="200"
    class="pima-cascader"
    :data="data"
    :load-data="loadData"
  />
</template>
