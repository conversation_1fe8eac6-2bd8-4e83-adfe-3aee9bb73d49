import _ from 'lodash';

import type { SearchSimpleModelType } from '^/types/job-post';
import type { PaginationParamsOption } from '@/helps/api';

import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { isNothing } from '@/utils/is';

type ListParamsType = SearchSimpleModelType & PaginationParamsOption;

/** 处理时间数据为 YYYY-MM-DD */
const formateToDate = (date: string | Date) => {
  const t = namespaceT('dateFormat');
  return dateFormatSTZ(new Date(date), t('date'));
};

/** 处理列表接口参数 */
export const handleListParams = (params:ListParamsType) => {
  const cloneParams = _.cloneDeep(params);


  cloneParams.creStartTime = !isNothing(cloneParams.creStartTime) ? formateToDate(cloneParams.creStartTime) : undefined;
  cloneParams.creEndTime = !isNothing(cloneParams.creEndTime) ? formateToDate(cloneParams.creEndTime) : undefined;

  return cloneParams;
};
