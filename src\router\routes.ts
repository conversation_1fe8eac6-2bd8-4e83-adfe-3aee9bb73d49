import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import { Auth } from '@/config/auth';

import TheRoot from '@/components/the-root.vue';


export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [

      /** 岗位发布 */
      {
        path: 'job-post',
        name: RN.JobPost,
        component: () => import('@/views/job-post'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobPost],
          baseAuthCodes: [Auth.JobPost.View],
        },
      },

      /** 岗位发布 新增 */
      {
        path: 'job-post/add',
        name: RN.JobPostAdd,
        component: () => import('@/views/job-post/children/add'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobPost],
          baseAuthCodes: [Auth.JobPost.View],
        },
      },

      /** 岗位发布 编辑 */
      {
        path: 'job-post/:id/edit',
        name: RN.JobPostEdit,
        component: () => import('@/views/job-post/children/add'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobPost],
          baseAuthCodes: [Auth.JobPost.View],
        },
      },

      /** 岗位发布 重新发起 */
      {
        path: 'job-post/:id/reapply',
        name: RN.JobPostReapply,
        component: () => import('@/views/job-post/children/add'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobPost],
          baseAuthCodes: [Auth.JobPost.View],
        },
      },


      /** 岗位发布 详情 */
      {
        path: 'job-post/:id/detail',
        name: RN.JobPostDetail,
        component: () => import('@/views/job-post/children/add'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobPost],
          baseAuthCodes: [Auth.JobPost.View],
        },
      },


      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
]);
