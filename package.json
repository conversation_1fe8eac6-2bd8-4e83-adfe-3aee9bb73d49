{"name": "sigs-double-selection-enterprise-nodejs", "version": "1.0.0", "private": true, "description": "学工系统-学生档案", "author": "<EMAIL>", "license": "ISC", "scripts": {"build": "yarn run build:client && yarn run build:server", "build:client": "cross-env NODE_ENV=production webpack --config ./build/webpack.client.prod.js", "build:server": "cross-env NODE_ENV=production webpack --config ./build/webpack.server.prod.js", "server": "cross-env NODE_ENV=production ts-node ./server/server.ts", "dev": "ts-node ./server/server.ts", "analyze": "cross-env report=1 yarn run build:client && yarn run build:server", "lint": "eslint --ext .js --ext .ts --ext .vue ./src/", "lint:fix": "eslint --ext .js --ext .ts --ext .vue ./src/ --fix", "lint:css": "stylelint ./src/**/*.{css,less,vue}"}, "dependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.25.9", "@babel/plugin-transform-object-rest-spread": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@babel/runtime": "^7.26.0", "axios": "^1.7.7", "babel-loader": "^9.2.1", "body-parser": "^1.20.3", "cas-authentication": "^0.0.8", "clean-webpack-plugin": "^4.0.0", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.39.0", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^5.0.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.1", "dotenv": "^16.4.5", "dotenv-webpack": "^8.1.0", "esbuild-loader": "^4.2.2", "express": "^4.21.2", "express-session": "^1.18.1", "file-saver": "^2.0.5", "helmet": "^7.2.0", "http-proxy-middleware": "^2.0.7", "ioredis": "^5.4.1", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "json-bigint": "^1.0.0", "less-loader": "^11.1.4", "lodash": "^4.17.21", "lru-cache": "^10.4.3", "mini-css-extract-plugin": "^2.9.2", "mitt": "^3.0.1", "morgan": "^1.10.0", "nanoid": "^3.3.8", "pinia": "^2.2.6", "postcss-loader": "^7.3.4", "postcss-preset-env": "^9.6.0", "qs": "^6.13.1", "redlock": "^4.2.0", "sanitize-html": "^2.13.0", "style-loader": "^3.3.4", "thread-loader": "^4.0.2", "ts-node": "^10.9.2", "urijs": "^1.19.11", "uuid": "^11.1.0", "view-ui-plus": "^1.3.19", "vue": "^3.5.17", "vue-i18n": "^10.0.5", "vue-loader": "^17.4.2", "vue-router": "^4.4.5", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/node": "^7.26.0", "@types/express": "^4.17.21", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^16.18.119", "@types/urijs": "^1.19.25", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.3", "eslint-import-resolver-webpack": "^0.13.9", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.31.0", "eslint-webpack-plugin": "^4.2.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "html-webpack-plugin": "^5.6.3", "less": "^4.2.0", "memory-fs": "^0.5.0", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "stylelint": "^15.11.0", "stylelint-config-rational-order-fix": "^0.1.9", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-less": "^2.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-less": "^2.0.0", "stylelint-order": "^6.0.4", "stylelint-webpack-plugin": "^4.1.1", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "typescript": "^5.6.3", "vue-eslint-parser": "^9.4.3", "webpack": "^5.96.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-dev-middleware": "^6.1.3", "webpack-hot-middleware": "^2.26.1", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.10.0"}, "resolutions": {"cas-authentication/xml2js": "^0.6.2", "cross-spawn": "^7.0.6", "micromatch": "^4.0.8", "nanoid": "^3.3.8"}, "engines": {"node": ">=16.18.0"}}