# Express 运行端口号
# 选填
EXPRESS_PORT=8080

# 服务 URL
# 选填
# 不填写默认为localhost地址
# 注：URL 不包含 PUBLIC_PATH
SERVICE_URL=http://localhost:8080

# 根目录
# 选填
PUBLIC_PATH=/double-selection-enterprise/

# 服务编码
# 必填
SERVICE_CODE=tsinghua-double-selection-enterprise

# 桌面服务编码
# 必填
DASHBOARD_SERVICE_CODE=tsinghua-stu-portal-home-dashboard

# 清深足迹 服务编码
# 必填
ACTIVITY_SERVICE_CODE=tsinghua-activity

# 客户端 ID
# 必填
CLIENT_ID= 

# 客户端密钥，必填
CLIENT_SECRET=

# 支持语言，选填
# 不填写默认所有支持的语言，支持多个，使用英文逗号分隔
# 如: zh_CN,en_US
SUPPORT_LOCALES=zh_CN,en_US

# 默认语言，选填
# 如: zh_CN
FALLBACK_LOCALE=zh_CN

### 接口相关 - 起始 ###
# 岗位双选接口 URL
# 必填
DOUBLE_SELECTION_API_BASE_URL=https://tsinghuadev.doocom.cn/double-selection_api

# 用户信息接口 URL
# 必填
USER_INFO_API_BASE_URL=https://tsinghuadev.doocom.cn/user-info_api

# 公共接口 URL
# 必填
BDC_CORE_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-core_api

# 统一数据 API URL
# 必填
BDC_ARCH_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-arch_api

# 统一应用接口API URL，必填
BDC_SERVICE_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-service_api

# 导出组件接口 URL，必填
BDC_EXPORT_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-export_api

# 导入组件接口 URL，必填
BDC_IMPORT_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-import_api

# 附件管理 API URL，必填
BDC_DFS_API_BASE_URL=https://tsinghuadev.doocom.cn/bdc-dfs_api

# 静态资源 URL
# 必填
STATIC_RESOURCES_BASE_URL=https://tsinghuadev.doocom.cn/static-resources

# 远程 UI 入口文件 URL
# Vue3版本
# 必填
REMOTE_UI_ENTRY_URL=https://tsinghuadev.doocom.cn/remote-ui-vue3/remoteEntry.js

# 远程编辑器入口文件 URL
# vue3版本
# 必填
REMOTE_EDITOR_ENTRY_URL=https://tsinghuadev.doocom.cn/remote-editor-vue3/remoteEntry.js

### 接口相关 - 结束 ###


### CAS 相关 - 起始 ###
# CAS 服务器 URL
# 必填
CAS_BASE_URL=https://tsinghuadev.doocom.cn/cas

# CAS 版本
# 必填
CAS_VERSION=5.2.3
### CAS 相关 - 结束 ###


### Redis 相关 - 起始 ###
# 是否开启 Redis Cluster
# 选填
# 值必须是 on 才开启
# 如: REDIS_CLUSTER=on
REDIS_CLUSTER=

# Redis 节点连接 URL
# 必填
# 当开启 Redis Cluster 时，支持多个 Redis 节点，使用英文逗号分隔
# 如：REDIS_URL=redis://root:123456@********:6379, redis://root:123456@********:6379, redis://root:123456@********:6379
# 当不开启 Redis Cluster 时，直接配置连接 URL
# 如：REDIS_URL=redis://root:123456@********:6379
REDIS_URL=redis://127.0.0.1:6379

# Redis Cluster NAT Mapping
# 选填
# Sometimes the cluster is hosted within a internal network
# that can only be accessed via a NAT (Network Address Translation) instance.
# Link: https://github.com/luin/ioredis#nat-mapping
# 当开启 Redis Cluster 时，同时存在内外网映射，此时需要配置该项
# 输入格式为规定格式：内网IP:端口号=>外网IP/域:端口号，支持多个映射，使用英文逗号分隔
# ********:30001=>************:6379
# ********:30001=>external-host-1.io:6379
# 如：REDIS_NAT_MAP=********:30001=>external-host-1.io:6379, ********:30002=>external-host-2.io:6379
REDIS_NAT_MAP=
### Redis 相关 - 结束 ###
