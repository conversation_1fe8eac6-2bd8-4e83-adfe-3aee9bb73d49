<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

import ClientOnly from '@/components/common/client-only';

import { RichTextEditorConfig } from '@/config/rich-text-editor';

// @ts-expect-error: Cannot find module 'pimaRemoteEditor/PimaEditor' or its corresponding type declarations.
// eslint-disable-next-line import/no-unresolved
const PimaEditor = defineAsyncComponent(() => import('pimaRemoteEditor/PimaEditor'));

</script>


<template>
  <ClientOnly>
    <PimaEditor
      v-bind="$attrs"
      :config="RichTextEditorConfig"
    />
  </ClientOnly>
</template>
