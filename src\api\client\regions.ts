import { CommonApi, RequestMethod, RequestParams } from '@/api/common/common-api';

interface RegionsItem {
  id: number,
  parentId: number,
  code: number,
  name: string,
  enName: string,
  hasChild: boolean,
  firstCn: string,
  firstEn: string
}


export class RegionsApi extends CommonApi<RegionsItem[]> {
  url() {
    return '/student-records/';
  }

  method(): RequestMethod {
    return 'POST';
  }

  defaultParams(): RequestParams {
    return {
      /** 默认传6892 获取中国数据 */
      parentId: 6892,
    };
  }

  async send(): Promise<RegionsItem[]> {
    const res = await super.send();
    console.log('%c [ res ]-33', 'font-size:13px; background:#fd3c64; color:#ff80a8;', res);
    return res;
  }
}
