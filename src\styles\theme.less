.pima-theme{
  background-color: var(--theme-color);
}

.pima-theme .pima-search-advanced-wrapper .ivu-row{
  padding: 0;
}

.pima-form .ivu-form-item .ivu-form-item{
  margin-bottom: 24px;
}


.pima-input-number.ivu-input-number{
  height: 42px;
  border-radius: 5px;

  .ivu-input-number-input-wrap{
    height: 42px;
  }

  .ivu-input-number-input{
    height: 42px;
    padding: 4px 33px 4px 16px;
  }

  .ivu-input-number-handler-wrap{
    .ivu-input-number-handler{
      height: 21px;
    }

    .ivu-input-number-handler-down-inner,
    .ivu-input-number-handler-up-inner{
      top:5px;

      &::before{
        color:var(--primary-color);
      }
    }

  }
}


.pima-wrapper-form-title .wrapper-form-title-body{
  .ivu-col:has(.detail-pair-label-item):not(:last-child){
    margin-bottom: 10px;
  }
}
